---
type: "agent_requested"
description: "Example description"
---

超级记忆 Rules
<关于记忆的引用>
对于每次聊天请求，第一步必须是读取并参考两个记忆文件：一个是用于记录错误及其修正的文件（位于.remember/memory/self.md ），另一个是用于存储用户偏好和自定义规则的文件（位置为.remember/memory/project.md ）。在生成每次回答之前，必须读取这两个文件以确保遵循已知的规则、用户偏好以及避免重复过去的错误。此外，在任务完成后，根据需要更新这两个文件是不可或缺的操作，以确保知识能够持续改进并应用于后续请求。
< 主要目标 >
Cursor 的目标是通过持久存储纠正和学习，避免重复已知的错误。为实现这一目标，系统使用两个记忆文件：
1..remember/memory/self.md ：记录所有遇到的错误及其修正方法。
2..remember/memory/project.md ：记录用户偏好和针对特定项目的自定义规则。
所有回答必须严格参考这些文件，以达成高效、精确且符合用户期望的结果。
< 纠错原则：从错误中学习 >
当检测到错误或收到废弃警告（deprecated warning）时，必须遵循以下步骤：
首先识别错误或次优的输出，通过对比最佳实践或者用户偏好确认问题点。
然后纠正错误，提供符合预期的正确解决方案。
最后，将错误和纠正方法记录到.remember/memory/self.md 文件中，以便未来避免重复同类问题。这些记录应该包括错误的简要描述、错误的代码或逻辑以及修正后的正确代码或逻辑，格式使用 <格式示例>
< 格式示例 >
Mistake: [Short Description]
Wrong:
[Insert incorrect code or logic]
Correct:
[Insert corrected code or logic]
</ 格式示例 >
</ 纠错原则：从错误中学习 >
< 必须遵守用户的偏好和项目规则 >
每次请求之前，系统都需要读取.remember/memory/project.md 文件，以了解用户的偏好和自定义规则。这可能包括以下信息：
用户不同工具的选择，例如偏好使用 Yarn 而非 npm，或选择 TypeScript 而不是 JavaScript。
用户的代码风格、命名规范、文件格式或特定的技术栈要求。
所有生成的回答都必须严格遵守用户在该文件中定义的这些偏好和规则。如果用户提出新的偏好，则需要及时更新该文件以确保未来请求的正确性。
<避免重复错误的机制>
在提供解决方案或代码之前，必须先检查.remember/memory/self.md 文件，看是否存在相关错误的修正记录。如果发现类似的错误已经被解决，必须按照已记录的修正方法提供解决方案，避免重新产生相同的错误。此外，建议将已应用的解决方案情况记录下来（作为调试或额外信息）。
< 保持记忆文件的清洁和及时更新 >
为了确保记忆系统的有效性，需要定期清理和更新.remember/memory/self.md 和.remember/memory/project.md 文件。当发现更优的修正方法时，应替换原有的修正记录。同时，为了使文件易于维护，需要用清晰的标题对内容进行分类，并根据主题进行分组。此外，记忆文件中的信息应具有普适性，避免过于具体的信息，从而确保存储的知识能够被高效重用。
< 文件存储路径 >
有关错误及其修正的方法储存在.remember/memory/self.md 文件中。用户特定的偏好和项目自定义规则则储存在.remember/memory/project.md 文件中。这两个文件是系统正常运行的重要组成部分，每次请求都需要参考和更新它们。
< 执行要求 >
如果在处理一个错误后没有读取或更新相关的记忆文件，这将被视为严重错误，可能导致回答质量下降或不符合用户期望的结果。在所有回答生成过程中，遵循已存储的知识和偏好，更新并维护记忆文件是不可绕过的流程。