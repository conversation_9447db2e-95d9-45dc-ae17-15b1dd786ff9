{"mcpServers": {"mcp-feedback-enhanced": {"command": "python", "args": ["-m", "mcp_feedback_enhanced", "server"], "timeout": 120000, "autoApprove": ["interact-enhanced"]}, "mysql": {"command": "npx", "args": ["-y", "@f4ww4z/mcp-mysql-server"], "env": {"MYSQL_HOST": "ajie.synology.me", "MYSQL_USER": "ajie", "MYSQL_PASSWORD": "wK9.VT68!m", "MYSQL_DATABASE": "qsdzy"}, "autoTrigger": {"enabled": true, "triggers": [{"pattern": "数据库|表|数据|查询|SQL", "action": "auto_analyze_database"}, {"pattern": "PHP|代码|函数|测试|性能", "action": "auto_test_php"}, {"pattern": "错误|异常|问题|调试", "action": "auto_monitor_status"}]}, "autoResponse": {"enabled": true, "rules": [{"when": "user_mentions_database", "auto_call": "analyze_database_structure", "priority": "high"}, {"when": "user_mentions_php_issues", "auto_call": "run_php_tests", "priority": "medium"}, {"when": "user_mentions_errors", "auto_call": "check_system_status", "priority": "high"}]}}}, "aiAssistant": {"autoTriggers": {"database_analysis": {"keywords": ["数据库", "表", "数据", "结构"], "action": "auto_analyze_database", "description": "自动分析数据库结构和数据规律"}, "php_testing": {"keywords": ["PHP", "代码", "测试", "性能"], "action": "auto_test_php_code", "description": "自动运行PHP代码测试和性能检测"}, "system_monitoring": {"keywords": ["错误", "异常", "状态", "监控"], "action": "auto_monitor_system", "description": "自动监控系统状态和错误信息"}}}}